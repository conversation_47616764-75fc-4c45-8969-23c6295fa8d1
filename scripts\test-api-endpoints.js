#!/usr/bin/env node

/**
 * Test script to verify all Payload CMS API endpoints
 * Run with: node scripts/test-api-endpoints.js
 */

const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'

// Test endpoints configuration
const endpoints = [
  // Content Collections (Public Read Access)
  { path: '/api/projects', method: 'GET', description: 'Get all projects' },
  { path: '/api/success-stories', method: 'GET', description: 'Get all success stories' },
  { path: '/api/events', method: 'GET', description: 'Get all events' },
  { path: '/api/media-gallery', method: 'GET', description: 'Get all media gallery items' },
  { path: '/api/resources', method: 'GET', description: 'Get all resources' },
  { path: '/api/news', method: 'GET', description: 'Get all news articles' },
  { path: '/api/partnerships', method: 'GET', description: 'Get all partnerships' },
  { path: '/api/partners', method: 'GET', description: 'Get all partners' },
  { path: '/api/investment-opportunities', method: 'GET', description: 'Get all investment opportunities' },
  { path: '/api/counties', method: 'GET', description: 'Get all counties' },

  // Admin Content Creation (requires authentication - will fail without auth)
  {
    path: '/api/projects',
    method: 'POST',
    description: 'Create new project (Admin only - will fail without auth)',
    body: {
      title: 'Test Project',
      description: 'This is a test project created via API',
      summary: 'Test project summary',
      category: 'knowledge-preservation',
      pillar: 'indigenous-knowledge',
      status: 'planning',
      published: false
    }
  },
  {
    path: '/api/news',
    method: 'POST',
    description: 'Create new news article (Admin only - will fail without auth)',
    body: {
      title: 'Test News Article',
      content: 'This is a test news article created via API',
      summary: 'Test news summary',
      category: 'general',
      published: false
    }
  },
  {
    path: '/api/events',
    method: 'POST',
    description: 'Create new event (Admin only - will fail without auth)',
    body: {
      title: 'Test Event',
      description: 'This is a test event created via API',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 86400000).toISOString(),
      location: 'Test Location',
      published: false
    }
  },
  
  // Form Submission Endpoints (Public POST)
  {
    path: '/api/contact-submissions',
    method: 'POST',
    description: 'Submit contact form',
    body: {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'API Test Submission',
      category: 'general',
      message: 'This is a test message from the API testing script.',
      priority: 'medium'
    }
  },
  {
    path: '/api/partnership-applications',
    method: 'POST',
    description: 'Submit partnership application',
    body: {
      organizationName: 'Test Organization',
      organizationType: 'private-company',
      contactName: 'Test Contact',
      email: '<EMAIL>',
      partnershipModel: 'strategic-partnership',
      priority: 'medium'
    }
  }
]

async function testEndpoint(endpoint) {
  const url = `${BASE_URL}${endpoint.path}`
  const options = {
    method: endpoint.method,
    headers: {
      'Content-Type': 'application/json',
    },
  }

  if (endpoint.body) {
    options.body = JSON.stringify(endpoint.body)
  }

  try {
    console.log(`\n🧪 Testing: ${endpoint.method} ${endpoint.path}`)
    console.log(`   Description: ${endpoint.description}`)
    
    const response = await fetch(url, options)
    const contentType = response.headers.get('content-type')
    
    let data
    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text()
    }

    if (response.ok) {
      console.log(`   ✅ Status: ${response.status} ${response.statusText}`)
      
      // Log relevant response info without overwhelming output
      if (endpoint.method === 'GET') {
        if (data && typeof data === 'object') {
          const keys = Object.keys(data)
          console.log(`   📊 Response keys: ${keys.join(', ')}`)
          
          // Log count if available
          const countKeys = keys.filter(key => key.includes('total') || key.includes('count'))
          countKeys.forEach(key => {
            console.log(`   📈 ${key}: ${data[key]}`)
          })
        }
      } else if (endpoint.method === 'POST') {
        console.log(`   📝 Response: ${JSON.stringify(data, null, 2)}`)
      }
    } else {
      console.log(`   ❌ Status: ${response.status} ${response.statusText}`)
      console.log(`   📄 Response: ${JSON.stringify(data, null, 2)}`)
    }
  } catch (error) {
    console.log(`   💥 Error: ${error.message}`)
  }
}

async function runTests() {
  console.log('🚀 Starting API Endpoint Tests')
  console.log(`🌐 Base URL: ${BASE_URL}`)
  console.log('=' .repeat(60))

  for (const endpoint of endpoints) {
    await testEndpoint(endpoint)
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  console.log('\n' + '='.repeat(60))
  console.log('✨ API Endpoint Tests Complete')
  console.log('\n📋 Summary:')
  console.log(`   • Tested ${endpoints.length} endpoints`)
  console.log('   • Check the output above for any failures')
  console.log('   • All endpoints should return 200 status for successful requests')
  console.log('\n🔗 Admin Interface:')
  console.log(`   • Access Payload Admin at: ${BASE_URL}/admin`)
  console.log('   • Login with your admin credentials to test CRUD operations')
}

// Run the tests
runTests().catch(console.error)
